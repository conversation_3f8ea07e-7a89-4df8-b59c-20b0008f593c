"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Edit3, Loader2, X } from "lucide-react"

export function EditNodeModal({ 
  isOpen, 
  onOpenChange, 
  nodeLabel,
  currentPosition = "",
  currentReward = "",
  onConfirm, 
  isLoading = false 
}) {
  const [position, setPosition] = useState(currentPosition)
  const [reward, setReward] = useState(currentReward)

  // Update local state when props change
  useEffect(() => {
    setPosition(currentPosition)
    setReward(currentReward)
  }, [currentPosition, currentReward, isOpen])

  const handleConfirm = () => {
    // Validate inputs
    if (!position.trim()) {
      return
    }

    const rewardValue = parseFloat(reward)
    if (isNaN(rewardValue) || rewardValue < 0) {
      return
    }

    onConfirm(position.trim(), rewardValue)
  }

  const handleCancel = () => {
    // Reset to original values
    setPosition(currentPosition)
    setReward(currentReward)
    onOpenChange(false)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleConfirm()
    } else if (e.key === "Escape") {
      handleCancel()
    }
  }

  const isValid = position.trim() && !isNaN(parseFloat(reward)) && parseFloat(reward) >= 0

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
            <Edit3 className="w-5 h-5" />
            ნოუდის რედაქტირება
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Node info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              რედაქტირება: <strong>"{nodeLabel}"</strong>
            </p>
          </div>

          {/* Position input */}
          <div className="space-y-2">
            <Label htmlFor="position" className="text-sm font-medium">
              პოზიცია *
            </Label>
            <Input
              id="position"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="შეიყვანეთ პოზიცია"
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Reward input */}
          <div className="space-y-2">
            <Label htmlFor="reward" className="text-sm font-medium">
              რიუორდი (%) *
            </Label>
            <Input
              id="reward"
              type="number"
              min="0"
              step="0.1"
              value={reward}
              onChange={(e) => setReward(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="შეიყვანეთ რიუორდი"
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Validation message */}
          {!isValid && (position || reward) && (
            <div className="text-sm text-red-600 dark:text-red-400">
              გთხოვთ შეავსოთ ყველა ველი სწორად
            </div>
          )}

          {/* Action buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
              disabled={isLoading}
            >
              გაუქმება
            </Button>
            <Button
              onClick={handleConfirm}
              className="flex-1"
              disabled={isLoading || !isValid}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ინახება...
                </>
              ) : (
                <>
                  <Edit3 className="w-4 h-4 mr-2" />
                  შენახვა
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
