"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { Loader2, Calendar, User, CheckCircle, Clock, AlertCircle, ChevronLeft, ChevronRight, Plus, ArrowLeft, FileText, Tag, Star, Users, Download, File, X, Trash } from "lucide-react"
import { CreateTaskForm } from "./create-task-form"
import { ApplicantsManagement } from "./applicants-management"
import { FireWorkerModal } from "./fire-worker-modal"
import { QuitJobModal } from "./quit-job-modal"

// Inline Badge component
const Badge = ({ variant = "default", className = "", children, ...props }) => {
  const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
  
  const variantClasses = {
    default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
    outline: "text-foreground"
  }
  
  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} {...props}>
      {children}
    </div>
  )
}

export function NodeTasksModal({ isOpen, onOpenChange, nodeId, nodeLabel, isUserOwnNode = false, nodeStatusId = null, nodeUser = null, nodeContractStatusId = null, childrenWithUsers = [], nodeOwnerInfo = null }) {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalTasks, setTotalTasks] = useState(0)
  const [perPage] = useState(8) // 8 tasks per page - 2 rows of 4 on desktop, 1 row of 4 on mobile
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showTaskDetail, setShowTaskDetail] = useState(false)
  const [selectedTask, setSelectedTask] = useState(null)
  const [showApplicants, setShowApplicants] = useState(false)
  const [showApplicantDetail, setShowApplicantDetail] = useState(false)
  const [selectedApplicant, setSelectedApplicant] = useState(null)
  const [applicantsCount, setApplicantsCount] = useState(0)
  const [loadingApplicants, setLoadingApplicants] = useState(false)
  const [lastView, setLastView] = useState('tasks') // 'tasks' or 'applicants'
  const [showFireWorkerModal, setShowFireWorkerModal] = useState(false)
  const [showQuitJobModal, setShowQuitJobModal] = useState(false)

  // Fetch tasks when modal opens or page changes
  useEffect(() => {
    if (isOpen && nodeId) {
      fetchTasks()
      // Fetch applicants count for child nodes with status 3
      console.log('useEffect check:', { isUserOwnNode, nodeStatusId, shouldFetch: !isUserOwnNode && nodeStatusId === 3 })
      if (!isUserOwnNode && nodeStatusId === 3) {
        console.log('Fetching applicants count...')
        fetchApplicantsCount()
      }
    }
  }, [isOpen, nodeId, currentPage, isUserOwnNode, nodeStatusId])

  const fetchTasks = async () => {
    setLoading(true)
    try {
      const response = await api.tasks.getNodeTasks(nodeId, perPage, currentPage)
      console.log('Tasks API Response:', response) // Debug log

      // Check if response has data structure (new API response format)
      if (response && response.data) {
        console.log('Tasks API Response:', response) // Debug log
        console.log('Tasks array:', response.data) // Debug log for tasks array
        console.log('Meta info:', response.meta) // Debug log for pagination meta

        // Filter out any null or invalid task objects
        const validTasks = Array.isArray(response.data)
          ? response.data.filter(task => task && typeof task === 'object' && task.id)
          : []

        console.log('Valid tasks after filtering:', validTasks) // Debug log
        setTasks(validTasks)

        // Get pagination info from meta object
        const meta = response.meta || {}
        setCurrentPage(meta.current_page || 1)
        setTotalPages(meta.last_page || 1)
        setTotalTasks(meta.total || 0)
      } else {
        console.log('No data found in response:', response) // Debug log
        setTasks([])
        setTotalTasks(0)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)

      // More detailed error logging
      if (error.message && error.message.includes('parent_id')) {
        console.error('Parent ID related error detected:', error)
        toast.error("ტასკების ჩატვირთვისას მონაცემთა სტრუქტურის პრობლემა")
      } else {
        toast.error("ტასკების ჩატვირთვა ვერ მოხერხდა")
      }
      setTasks([])
    } finally {
      setLoading(false)
    }
  }

  const fetchApplicantsCount = async () => {
    console.log('Fetching applicants count for nodeId:', nodeId)
    setLoadingApplicants(true)
    try {
      const response = await api.network.getApplicants(nodeId)
      console.log('Applicants count response:', response)
      if (response && response.data) {
        const applicantsData = Array.isArray(response.data) ? response.data : []
        console.log('Applicants data:', applicantsData, 'Count:', applicantsData.length)
        setApplicantsCount(applicantsData.length)
      } else {
        console.log('No applicants data found')
        setApplicantsCount(0)
      }
    } catch (error) {
      console.error('Failed to fetch applicants count:', error)
      setApplicantsCount(0)
    } finally {
      setLoadingApplicants(false)
    }
  }

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
    }
  }

  const handleCreateTask = () => {
    setShowCreateForm(true)
  }

  const handleCancelCreate = () => {
    setShowCreateForm(false)
    setLastView('tasks')
  }

  const handleTaskCreated = () => {
    setShowCreateForm(false)
    setCurrentPage(1) // Reset to first page
    fetchTasks() // Refresh tasks list
  }

  const handleFireWorker = () => {
    setShowFireWorkerModal(true)
  }

  const handleFireWorkerSuccess = () => {
    // Refresh the page or update the UI as needed
    window.dispatchEvent(new CustomEvent('networkDataUpdated'))
    onOpenChange(false) // Close the tasks modal
  }

  const handleQuitJob = () => {
    setShowQuitJobModal(true)
  }

  const handleQuitJobSuccess = () => {
    // Reload network data after successful quitting
    window.dispatchEvent(new CustomEvent('networkDataUpdated'))
    setShowQuitJobModal(false)
    onOpenChange(false) // Close the modal
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  const handleBackToTasksList = () => {
    setShowTaskDetail(false)
    setSelectedTask(null)
    setLastView('tasks')
  }

  const handleOpenApplicants = () => {
    setShowApplicants(true)
    setLastView('applicants')
  }

  const handleViewApplicantDetails = (applicant) => {
    console.log('handleViewApplicantDetails called with:', applicant)
    setSelectedApplicant(applicant)
    setShowApplicants(false) // Hide applicants list
    setShowApplicantDetail(true) // Show detail view
    console.log('States set - showApplicants:', false, 'showApplicantDetail:', true, 'selectedApplicant:', applicant)
  }

  const handleBackToApplicants = () => {
    console.log('handleBackToApplicants called')
    setShowApplicantDetail(false)
    setSelectedApplicant(null)
    setShowApplicants(true) // Show applicants list again
    console.log('States set - showApplicants:', true, 'showApplicantDetail:', false)
  }

  const handleModalClose = (open) => {
    if (!open) {
      // Reset all states when modal closes
      setShowCreateForm(false)
      setShowTaskDetail(false)
      setSelectedTask(null)
      setShowApplicants(false)
      setShowApplicantDetail(false)
      setSelectedApplicant(null)
      // Don't reset lastView - keep it for next time modal opens
    }
    onOpenChange(open)
  }

  // When modal opens, restore to last view if it was applicants
  useEffect(() => {
    if (isOpen && lastView === 'applicants' && !isUserOwnNode && nodeStatusId === 3) {
      setShowApplicants(true)
    }
  }, [isOpen, lastView, isUserOwnNode, nodeStatusId])

  const getStatusBadge = (statusId) => {
    switch (statusId) {
      case 1:
        return <Badge variant="default" className="bg-green-500 hover:bg-green-600"><CheckCircle className="w-3 h-3 mr-1" />დასრულებული</Badge>
      case 2:
        return <Badge variant="secondary" className="bg-blue-500 hover:bg-blue-600 text-white"><Clock className="w-3 h-3 mr-1" />მიმდინარე</Badge>
      case 0:
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600"><AlertCircle className="w-3 h-3 mr-1" />მოლოდინში</Badge>
      default:
        return <Badge variant="outline">სტატუსი: {statusId}</Badge>
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('ka-GE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // Format weekday helper
  const formatWeekday = (weekday) => {
    const weekdays = [
      "კვირა", "ორშაბათი", "სამშაბათი", "ოთხშაბათი",
      "ხუთშაბათი", "პარასკევი", "შაბათი"
    ]
    return weekdays[weekday] || "უცნობი"
  }

  // Format time helper
  const formatTime = (hour, minute, second = 0) => {
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`
  }

  // Get plain text from HTML for preview
  const getPlainTextPreview = (html, maxLength = 100) => {
    if (!html) return ""
    const div = document.createElement('div')
    div.innerHTML = html
    const text = div.textContent || div.innerText || ""
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text
  }

  const renderSliderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <div className="mt-6 flex items-center justify-center gap-4">
        {/* Previous Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 hover:bg-primary hover:text-primary-foreground border-2"
        >
          <ChevronLeft className="w-4 h-4" />
          <span className="hidden sm:inline">წინა</span>
        </Button>

        {/* Page Indicator with Slider Effect */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {/* Page dots */}
            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, index) => {
                const pageNum = index + 1
                const isActive = pageNum === currentPage
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`
                      w-3 h-3 rounded-full transition-all duration-300 ease-in-out cursor-pointer
                      ${isActive
                        ? 'bg-primary scale-125 shadow-lg ring-2 ring-primary/30'
                        : 'bg-muted-foreground/30 hover:bg-muted-foreground/60 hover:scale-110'
                      }
                    `}
                    aria-label={`გვერდი ${pageNum}`}
                  />
                )
              })}
            </div>

            {/* Page counter */}
            <div className="text-sm text-muted-foreground font-medium bg-muted/50 px-3 py-1 rounded-full">
              {currentPage} / {totalPages}
            </div>
          </div>
        </div>

        {/* Next Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 hover:bg-primary hover:text-primary-foreground border-2"
        >
          <span className="hidden sm:inline">შემდეგი</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    )
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleModalClose}>
      <DialogContent className="sm:max-w-[900px] lg:max-w-[1100px] w-[900px] lg:w-[1100px] h-[600px] max-h-[85vh] overflow-hidden flex flex-col">
        {showCreateForm ? (
          // Create Task Form View
          <>
            <DialogHeader>
              <DialogTitle className="sr-only">ახალი ტასკის შექმნა - {nodeLabel}</DialogTitle>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto p-2">
              <CreateTaskForm
                nodeId={nodeId}
                nodeLabel={nodeLabel}
                onCancel={handleCancelCreate}
                onTaskCreated={handleTaskCreated}
              />
            </div>
          </>
        ) : showApplicants ? (
          // Applicants View
          <>
            <DialogHeader>
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowApplicants(false)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  უკან
                </Button>
                <DialogTitle className="text-xl font-bold flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  {nodeLabel} - აპლიკანტები
                  {applicantsCount > 0 && (
                    <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                      {applicantsCount}
                    </Badge>
                  )}
                </DialogTitle>
              </div>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              <ApplicantsManagement
                isOpen={true}
                onOpenChange={(shouldRefresh) => {
                  setShowApplicants(false)
                  if (shouldRefresh) {
                    fetchApplicantsCount()
                  }
                }}
                nodeId={nodeId}
                nodeLabel={nodeLabel}
                embedded={true}
                onViewDetails={handleViewApplicantDetails}
              />
            </div>
          </>
        ) : (() => {
          console.log('Checking showApplicantDetail:', showApplicantDetail, 'selectedApplicant:', selectedApplicant)
          return showApplicantDetail && selectedApplicant
        })() ? (
          // Applicant Detail View
          <>
            <DialogHeader>
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToApplicants}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  უკან
                </Button>
                <DialogTitle className="text-xl font-bold flex items-center gap-2">
                  <User className="w-5 h-5" />
                  {selectedApplicant && (
                    <>
                      {selectedApplicant.user?.first_name && selectedApplicant.user?.last_name
                        ? `${selectedApplicant.user.first_name} ${selectedApplicant.user.last_name}`
                        : `მომხმარებელი #${selectedApplicant.user?.id || selectedApplicant.worker_id}`
                      } - დეტალები
                    </>
                  )}
                </DialogTitle>
              </div>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto p-6">
              <ApplicantsManagement
                isOpen={true}
                onOpenChange={() => {}}
                nodeId={nodeId}
                nodeLabel={nodeLabel}
                embedded={true}
                detailMode={true}
                selectedApplicant={selectedApplicant}
              />
            </div>
          </>
        ) : showTaskDetail && selectedTask ? (
          // Task Detail View
          <>
            <DialogHeader>
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToTasksList}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  უკან
                </Button>
                <DialogTitle className="text-xl font-bold flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  ტასკის დეტალები
                </DialogTitle>
              </div>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto p-2">
              <div className="space-y-6">
                {/* Task Header */}
                <div className="border rounded-lg p-6 bg-card">
                  <div className="flex items-start justify-between mb-4">
                    <h2 className="text-2xl font-bold">ტასკი #{selectedTask.id}</h2>
                    {getStatusBadge(selectedTask.status_id)}
                  </div>

                  {(selectedTask.input_text || selectedTask.task_description) && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">ტასკის აღწერა</h3>
                      <div
                        className="text-base leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: selectedTask.input_text || selectedTask.task_description }}
                      />
                    </div>
                  )}

                  {(selectedTask.output_text || selectedTask.result_description) && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">შედეგი</h3>
                      <div
                        className="text-base leading-relaxed prose prose-sm max-w-none bg-green-50 p-4 rounded-lg border border-green-200"
                        dangerouslySetInnerHTML={{ __html: selectedTask.output_text || selectedTask.result_description }}
                      />
                    </div>
                  )}

                  {/* Input File */}
                  {(selectedTask.input_file || selectedTask.task_file) && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">ტასკის ფაილი</h3>
                      <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <File className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">
                          {selectedTask.input_file || selectedTask.task_file}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="ml-auto"
                          onClick={() => {
                            const fileUrl = selectedTask.input_file || selectedTask.task_file
                            if (fileUrl) {
                              window.open(fileUrl, '_blank')
                            }
                          }}
                        >
                          <Download className="w-3 h-3 mr-1" />
                          ჩამოტვირთვა
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Output File */}
                  {(selectedTask.output_file || selectedTask.result_file) && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">შედეგის ფაილი</h3>
                      <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <File className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          {selectedTask.output_file || selectedTask.result_file}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="ml-auto"
                          onClick={() => {
                            const fileUrl = selectedTask.output_file || selectedTask.result_file
                            if (fileUrl) {
                              window.open(fileUrl, '_blank')
                            }
                          }}
                        >
                          <Download className="w-3 h-3 mr-1" />
                          ჩამოტვირთვა
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Task Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Basic Information */}
                  <div className="border rounded-lg p-4 bg-card">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Tag className="w-4 h-4" />
                      ძირითადი ინფორმაცია
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm text-muted-foreground">ტიპი:</span>
                        <Badge variant="outline" className="ml-2">
                          {selectedTask.type === 0 ? "განმეორებადი" : "ერთჯერადი"}
                        </Badge>
                      </div>

                      {selectedTask.rate && (
                        <div>
                          <span className="text-sm text-muted-foreground">შეფასება:</span>
                          <div className="flex items-center gap-1 ml-2">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span className="font-medium">{selectedTask.rate}</span>
                          </div>
                        </div>
                      )}

                      {selectedTask.worker_id && (
                        <div>
                          <span className="text-sm text-muted-foreground">მუშაკის ID:</span>
                          <Badge variant="outline" className="ml-2">
                            <User className="w-3 h-3 mr-1" />
                            {selectedTask.worker_id}
                          </Badge>
                        </div>
                      )}

                      <div>
                        <span className="text-sm text-muted-foreground">სტატუსი:</span>
                        <Badge variant={selectedTask.is_active ? "default" : "secondary"} className="ml-2">
                          {selectedTask.is_active ? "აქტიური" : "არააქტიური"}
                        </Badge>
                      </div>

                      {selectedTask.duration && (
                        <div>
                          <span className="text-sm text-muted-foreground">ხანგრძლივობა:</span>
                          <span className="ml-2 text-sm font-medium">{selectedTask.duration} წუთი</span>
                        </div>
                      )}

                      {selectedTask.started_at && (
                        <div>
                          <span className="text-sm text-muted-foreground">დაწყების დრო:</span>
                          <p className="text-sm font-medium">{formatDate(selectedTask.started_at)}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Timing Information */}
                  <div className="border rounded-lg p-4 bg-card">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      დროის ინფორმაცია
                    </h3>
                    <div className="space-y-3">
                      {selectedTask.created_at && (
                        <div>
                          <span className="text-sm text-muted-foreground">შექმნის თარიღი:</span>
                          <p className="text-sm font-medium">{formatDate(selectedTask.created_at)}</p>
                        </div>
                      )}

                      {selectedTask.updated_at && (
                        <div>
                          <span className="text-sm text-muted-foreground">ბოლო განახლება:</span>
                          <p className="text-sm font-medium">{formatDate(selectedTask.updated_at)}</p>
                        </div>
                      )}

                      {selectedTask.end_date && (
                        <div>
                          <span className="text-sm text-muted-foreground">ვადა:</span>
                          <p className="text-sm font-medium">{formatDate(selectedTask.end_date)}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Schedule Information (for recurring tasks) */}
                  {selectedTask.type === 0 && (
                    <div className="border rounded-lg p-4 bg-card md:col-span-2">
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        განრიგი (განმეორებადი ტასკი)
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {selectedTask.weekday !== undefined && (
                          <div>
                            <span className="text-sm text-muted-foreground">კვირის დღე:</span>
                            <p className="text-sm font-medium">{formatWeekday(selectedTask.weekday)}</p>
                          </div>
                        )}

                        {(selectedTask.hour !== undefined || selectedTask.minute !== undefined) && (
                          <div>
                            <span className="text-sm text-muted-foreground">დრო:</span>
                            <p className="text-sm font-medium">
                              {formatTime(selectedTask.hour || 0, selectedTask.minute || 0, selectedTask.second || 0)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          // Tasks List View
          <>
            <DialogHeader>
              {/* Debug info */}
              {(() => {
                console.log('🔍 Modal debug:', {
                  isUserOwnNode,
                  nodeStatusId,
                  nodeUser,
                  nodeContractStatusId,
                  childrenWithUsers,
                  nodeId,
                  nodeLabel,
                  nodeOwnerInfo,
                  'nodeUser.contract_id': nodeUser?.contract_id,
                  'nodeUser.contracts': nodeUser?.contracts,
                  'nodeUser.contracts[0].id': nodeUser?.contracts?.[0]?.id,
                  'nodeOwnerInfo.contract_id': nodeOwnerInfo?.contract_id
                })
                return null // Just for debug, don't render anything
              })()}

              {/* Show applicants button for my nodes with vacant child positions */}
              {isUserOwnNode && nodeStatusId === 3 && (
                <div className="flex items-center justify-start mb-4">
                  <Button
                    onClick={handleOpenApplicants}
                    size="sm"
                    variant="outline"
                    disabled={loadingApplicants}
                    className="flex items-center gap-3 px-4 py-2 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600 text-white border-none hover:from-blue-600 hover:via-purple-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-70 disabled:hover:scale-100 rounded-xl backdrop-blur-sm"
                    title={`ნახეთ და მართეთ აპლიკანტები${applicantsCount > 0 ? ` (${applicantsCount} მოთხოვნა)` : ''}`}
                  >
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      {loadingApplicants ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Users className="w-4 h-4" />
                      )}
                    </div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium">აპლიკანტები</span>
                      {!loadingApplicants && applicantsCount > 0 && (
                        <span className="text-xs opacity-90">{applicantsCount} მოთხოვნა</span>
                      )}
                    </div>
                    {!loadingApplicants && applicantsCount > 0 && (
                      <Badge variant="secondary" className="ml-1 bg-white/90 text-blue-600 font-bold text-xs">
                        {applicantsCount}
                      </Badge>
                    )}
                  </Button>
                </div>
              )}

              {/* Top section with applicants button for child nodes with status 3 */}
              {(() => {
                console.log('Button render check:', { isUserOwnNode, nodeStatusId, applicantsCount, loadingApplicants })
                return !isUserOwnNode && nodeStatusId === 3
              })() && (
                <div className="flex items-center justify-start mb-4">
                  <Button
                    onClick={handleOpenApplicants}
                    size="sm"
                    variant="outline"
                    disabled={loadingApplicants}
                    className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none hover:from-blue-600 hover:to-purple-700 shadow-lg transition-all duration-200 hover:scale-105 disabled:opacity-70 disabled:hover:scale-100"
                    title={`ნახეთ და მართეთ აპლიკანტები${applicantsCount > 0 ? ` (${applicantsCount} მოთხოვნა)` : ''}`}
                  >
                    {loadingApplicants ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Users className="w-4 h-4" />
                    )}
                    აპლიკანტები
                    {!loadingApplicants && applicantsCount > 0 && (
                      <Badge variant="secondary" className="ml-1 bg-white text-blue-600 font-bold">
                        {applicantsCount}
                      </Badge>
                    )}
                  </Button>
                </div>
              )}

              <DialogTitle className="text-xl font-bold text-center flex items-center justify-center gap-2">
                <User className="w-5 h-5" />
                {nodeLabel} - ტასკები
              </DialogTitle>
              <div className="flex items-center justify-between">
                {/* Left side - User info or children info or task count */}
                <div className="flex items-center gap-3">
                  {/* Show node owner info for my own nodes */}
                  {isUserOwnNode && nodeOwnerInfo && (
                    <div className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg shadow-sm">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-md">
                        <User className="w-3 h-3 text-white" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs font-medium text-blue-800 dark:text-blue-200">
                          ნოუდის მფლობელი
                        </span>
                        <span className="text-xs font-semibold text-blue-900 dark:text-blue-100">
                          მომხმარებელი #{nodeOwnerInfo.parent_master_user_id}
                        </span>
                      </div>
                      {/* Quit job button for my own nodes */}
                      <button
                        onClick={handleQuitJob}
                        title="თანამშრომლობის გაწყვეტა"
                        className="ml-2 w-8 h-8 flex items-center justify-center bg-red-100 dark:bg-red-800/30 hover:bg-red-200 dark:hover:bg-red-700 transition-all duration-300 ease-out cursor-pointer text-red-700 dark:text-red-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-400 rounded-lg"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  )}

                  {/* Show children info for my nodes */}
                  {isUserOwnNode && childrenWithUsers && childrenWithUsers.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {childrenWithUsers.slice(0, 3).map((child, index) => (
                        <div key={child.id || index} className="flex items-center gap-2 px-2 py-1 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg shadow-sm">
                          <div className="w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-md">
                            <User className="w-3 h-3 text-white" />
                          </div>
                          <div className="flex flex-col">
                            <span className="text-xs font-medium text-green-800 dark:text-green-200">
                              {child.position || `ნოუდი #${child.id}`}
                            </span>
                            <span className="text-xs font-semibold text-green-900 dark:text-green-100">
                              {child.node_user.first_name && child.node_user.last_name
                                ? `${child.node_user.first_name} ${child.node_user.last_name}`
                                : `მომხმარებელი #${child.node_user.id}`
                              }
                            </span>
                          </div>
                        </div>
                      ))}
                      {childrenWithUsers.length > 3 && (
                        <div className="flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
                          <span className="text-xs text-muted-foreground">
                            +{childrenWithUsers.length - 3} სხვა
                          </span>
                        </div>
                      )}
                    </div>
                  ) : !isUserOwnNode && nodeStatusId !== 3 && nodeUser && nodeUser.id ? (
                    /* Show user info for assigned child nodes */
                    <div className={`flex flex-row items-stretch w-full max-w-full sm:max-w-md rounded-xl shadow-md overflow-hidden transition-transform hover:scale-[1.015] hover:shadow-lg duration-300 ease-out ${
                      nodeContractStatusId === 5
                        ? 'bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border border-yellow-200 dark:border-yellow-800'
                        : 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800'
                    }`}>

                      {/* იუზერის ინფო */}
                      <div className="flex items-center gap-3 sm:gap-4 px-3 sm:px-5 py-3 flex-1 min-w-0">
                        <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center shadow-lg flex-shrink-0 relative ${
                          nodeContractStatusId === 5
                            ? 'bg-gradient-to-r from-yellow-500 to-amber-600'
                            : 'bg-gradient-to-r from-green-500 to-emerald-600'
                        }`}>
                          <span className="text-white font-bold text-xs sm:text-sm">#{nodeUser.id}</span>
                        </div>

                        <div className="flex flex-col overflow-hidden min-w-0 flex-1">
                          {/* მომხმარებელი ID - მხოლოდ დესკტოპზე */}
                          <span className={`hidden sm:block text-[0.65rem] font-medium whitespace-nowrap truncate ${
                            nodeContractStatusId === 5
                              ? 'text-yellow-800 dark:text-yellow-200'
                              : 'text-green-800 dark:text-green-200'
                          }`}>
                            მომხმარებელი:
                          </span>
                          <span className={`hidden sm:block text-base font-semibold truncate ${
                            nodeContractStatusId === 5
                              ? 'text-yellow-900 dark:text-yellow-100'
                              : 'text-green-900 dark:text-green-100'
                          }`}>
                            {`#${nodeUser.id}`}
                          </span>

                          {/* სტატისტიკა: leave, reject, rate */}
                          <div className={`flex flex-wrap gap-2 sm:gap-3 sm:mt-0.5 text-[0.65rem] sm:text-xs font-semibold leading-none ${
                            nodeContractStatusId === 5
                              ? 'text-yellow-700 dark:text-yellow-300'
                              : 'text-green-700 dark:text-green-300'
                          }`}>
                            <span title="სამსახურიდან გაგდება" className="whitespace-nowrap">🌿 {nodeUser.leave_count ?? 0}</span>
                            <span title="უარი თქვეს" className="whitespace-nowrap">⛔ {nodeUser.reject_count ?? 0}</span>
                            <span title="შეფასება" className="whitespace-nowrap">⭐ {nodeUser.rate ?? 0}</span>
                          </div>
                        </div>
                      </div>

                      {/* წაშლის ღილაკის არეა / ლოადერი */}
                      {nodeContractStatusId === 5 ? (
                        <div
                          title="მიმდინარე დავალებების დასრულების შემდეგ მომხმარებელი ავტომატურად დატოვებს ადგილს"
                          className="w-auto min-w-[56px] flex items-center justify-center bg-yellow-100 dark:bg-yellow-800/30 transition-all duration-300 ease-out text-yellow-700 dark:text-yellow-300 rounded-r-xl cursor-help"
                        >
                          <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                        </div>
                      ) : (
                        <button
                          onClick={handleFireWorker}
                          title="დაითხოვე თანამშრომელი"
                          className="w-auto min-w-[56px] flex items-center justify-center bg-red-100 dark:bg-red-800/30 hover:bg-red-200 dark:hover:bg-red-700 transition-all duration-300 ease-out cursor-pointer text-red-700 dark:text-red-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-400 rounded-r-xl"
                        >
                          <Trash className="w-4 h-4 sm:w-5 sm:h-5" />
                        </button>
                      )}
                    </div>





                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {/* სულ: {totalTasks} ტასკი */}
                    </p>
                  )}
                </div>

                {/* Right side - Action button */}
                {!isUserOwnNode && (
                  <Button
                    onClick={handleCreateTask}
                    size="sm"
                    disabled={nodeContractStatusId === 5}
                    className={`flex items-center gap-2 ${
                      nodeContractStatusId === 5
                        ? 'opacity-50 cursor-not-allowed'
                        : ''
                    }`}
                    title={nodeContractStatusId === 5
                      ? "ახალი ტასკის დამატება შეუძლებელია - თანამშრომელი მალე დატოვებს პოზიციას"
                      : "ახალი ტასკის დამატება"
                    }
                  >
                    <Plus className="w-4 h-4" />
                    <span className="hidden sm:inline">ახალი ტასკი</span>
                  </Button>
                )}
              </div>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">ტასკების ჩატვირთვა...</span>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-12">
                  <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">ტასკები არ მოიძებნა</h3>
                  <p className="text-muted-foreground mb-4">
                    ამ ნოუდისთვის ჯერ არ არის მინიჭებული ტასკები
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-2">
                  {tasks.map((task) => (
                    <div
                      key={task.id}
                      onClick={() => handleTaskClick(task)}
                      className="border rounded-xl p-4 hover:shadow-xl transition-all duration-300 bg-card hover:scale-[1.02] group cursor-pointer hover:border-primary/50 backdrop-blur-sm"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="font-semibold text-base leading-tight group-hover:text-primary transition-colors">
                          ტასკი #{task.id}
                        </h3>
                        {getStatusBadge(task.status_id)}
                      </div>

                      {(task.input_text || task.task_description) && (
                        <p className="text-muted-foreground mb-3 leading-relaxed text-sm line-clamp-3">
                          {getPlainTextPreview(task.input_text || task.task_description, 80)}
                        </p>
                      )}

                      <div className="space-y-2">
                        {task.created_at && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Calendar className="w-3 h-3" />
                            <span>შექმნილია: {formatDate(task.created_at)}</span>
                          </div>
                        )}

                        {task.end_date && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>ვადა: {formatDate(task.end_date)}</span>
                          </div>
                        )}

                        <div className="flex flex-wrap gap-1">
                          {task.type !== undefined && (
                            <Badge variant="outline" className="text-xs">
                              {task.type === 0 ? "განმეორებადი" : "ერთჯერადი"}
                            </Badge>
                          )}

                          {task.rate && (
                            <Badge variant="outline" className="text-xs">
                              შეფასება: {task.rate}
                            </Badge>
                          )}

                          {!task.is_active && (
                            <Badge variant="secondary" className="text-xs">
                              არააქტიური
                            </Badge>
                          )}

                          {(task.input_file || task.task_file) && (
                            <Badge variant="outline" className="text-xs flex items-center gap-1">
                              <File className="w-2 h-2" />
                              ფაილი
                            </Badge>
                          )}

                          {(task.output_text || task.result_description || task.output_file || task.result_file) && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                              შედეგი
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {renderSliderPagination()}
          </>
        )}
      </DialogContent>
      </Dialog>

      {/* Fire Worker Modal */}
      <FireWorkerModal
        isOpen={showFireWorkerModal}
        onOpenChange={setShowFireWorkerModal}
        contractId={(() => {
          // Try to get contract_id from different sources
          if (nodeUser?.contracts && Array.isArray(nodeUser.contracts) && nodeUser.contracts.length > 0) {
            // Find active contract (status_id = 2) or use first contract
            const activeContract = nodeUser.contracts.find(contract => contract && contract.status_id === 2) || nodeUser.contracts[0]
            console.log('🔥 Fire Worker - Using contract from contracts array:', activeContract?.id)
            return activeContract?.id
          }
          // Fallback to direct contract_id if available
          console.log('🔥 Fire Worker - Using direct contract_id:', nodeUser?.contract_id)
          return nodeUser?.contract_id
        })()}
        workerName={`#${nodeUser?.id}`}
        onSuccess={handleFireWorkerSuccess}
      />

      {/* Quit Job Modal */}
      <QuitJobModal
        isOpen={showQuitJobModal}
        onOpenChange={setShowQuitJobModal}
        contractId={nodeOwnerInfo?.contract_id}
        nodeLabel={nodeLabel}
        onSuccess={handleQuitJobSuccess}
      />
    </>
  )
}
