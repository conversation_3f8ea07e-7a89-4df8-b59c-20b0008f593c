// API service for backend communication
const API_BASE_URL = 'http://127.0.0.1:8000/api'

// Token and user management
export const tokenManager = {
  getToken: () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token')
    }
    return null
  },

  setToken: (token) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token)
    }
  },

  removeToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_id')
    }
  },

  getUserId: () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('user_id')
    }
    return null
  },

  setUserId: (userId) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_id', userId)
    }
  },

  isAuthenticated: () => {
    return !!tokenManager.getToken()
  }
}

// Base API client
class ApiClient {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const token = tokenManager.getToken()

    const config = {
      headers: {
        'Accept': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // Add authorization header if token exists
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, config)

      // Handle different response types
      const contentType = response.headers.get('content-type')
      let data

      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json()
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError)
          throw new ApiError('Invalid JSON response from server', response.status, jsonError)
        }
      } else {
        data = await response.text()
      }

      if (!response.ok) {
        // Enhanced error handling for specific error types
        let errorMessage = data?.message || `HTTP error! status: ${response.status}`

        // Check for specific parent_id related errors
        if (typeof data === 'string' && data.includes('parent_id')) {
          errorMessage = 'Data structure error: Invalid parent_id reference'
        } else if (data?.error && typeof data.error === 'string' && data.error.includes('parent_id')) {
          errorMessage = 'Data structure error: Invalid parent_id reference'
        }

        throw new ApiError(errorMessage, response.status, data)
      }

      // Validate response data structure for critical endpoints
      if (endpoint.includes('/tasks') && data && data.data && data.data.data) {
        // Filter out any null or invalid objects from tasks data
        if (Array.isArray(data.data.data)) {
          data.data.data = data.data.data.filter(item => item && typeof item === 'object')
        }
      }

      return data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      // Enhanced error handling for specific error types
      let errorMessage = 'Network error occurred'
      if (error.message && error.message.includes('parent_id')) {
        errorMessage = 'Data structure error: Invalid parent_id reference'
      }

      throw new ApiError(errorMessage, 0, error)
    }
  }

  async get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options })
  }

  async post(endpoint, data, options = {}) {
    const isFormData = data instanceof FormData
    
    const config = {
      method: 'POST',
      ...options,
    }

    if (isFormData) {
      config.body = data
    } else {
      config.headers = {
        'Content-Type': 'application/json',
        ...options.headers,
      }
      config.body = JSON.stringify(data)
    }

    return this.request(endpoint, config)
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: JSON.stringify(data),
      ...options,
    })
  }

  async patch(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: JSON.stringify(data),
      ...options,
    })
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options })
  }
}

// Custom error class
export class ApiError extends Error {
  constructor(message, status, data) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

// Create API client instance
const apiClient = new ApiClient()

// Authentication API
export const authAPI = {
  async register(userData) {
    const formData = new FormData()
    formData.append('name', userData.name)
    formData.append('email', userData.email)
    formData.append('password', userData.password)
    formData.append('birth_date', userData.birth_date)
    formData.append('gender', userData.gender)

    const response = await apiClient.post('/register', formData)

    if (response.success && response.data?.token) {
      tokenManager.setToken(response.data.token)

      // Save user ID if it exists in the response
      if (response.data?.user?.id) {
        tokenManager.setUserId(response.data.user.id)
      } else if (response.data?.id) {
        tokenManager.setUserId(response.data.id)
      }
    }

    return response
  },

  async login(credentials) {
    const formData = new FormData()
    formData.append('email', credentials.email)
    formData.append('password', credentials.password)

    const response = await apiClient.post('/login', formData)

    if (response.success && response.data?.token) {
      tokenManager.setToken(response.data.token)

      // Save user ID if it exists in the response
      if (response.data?.user?.id) {
        tokenManager.setUserId(response.data.user.id)
      } else if (response.data?.id) {
        tokenManager.setUserId(response.data.id)
      }
    }

    return response
  },

  async logout() {
    try {
      await apiClient.post('/logout')
    } catch (error) {
      // Even if logout fails on server, clear local token
      console.warn('Logout request failed:', error)
    } finally {
      tokenManager.removeToken()
    }
  },

  async getCurrentUser() {
    // This endpoint might not exist in the backend yet
    // return apiClient.get('/user')
    throw new Error('getCurrentUser endpoint not implemented')
  }
}

// Network/Diagram API
export const networkAPI = {
  async getNetwork() {
    return apiClient.get('/network')
  },

  async createNode(nodeData) {
    return apiClient.post('/network', nodeData)
  },

  async addNode(nodeData) {
    const formData = new FormData()
    formData.append('position', nodeData.position)
    formData.append('reward', nodeData.reward)
    formData.append('node_id', nodeData.node_id)

    return apiClient.post('/add-node', formData)
  },

  async updateNode(nodeId, nodeData) {
    return apiClient.put(`/network/${nodeId}`, nodeData)
  },

  async deleteNode(nodeId) {
    return apiClient.delete(`/network/${nodeId}`)
  },

  async applyToNode(nodeId) {
    const formData = new FormData()
    formData.append('node_id', nodeId)

    return apiClient.post('/node/apply', formData)
  },

  async getApplicants(nodeId, perPage = 10, page = 1) {
    const params = new URLSearchParams({
      per_page: perPage.toString(),
      page: page.toString()
    })
    return apiClient.get(`/applicants/${nodeId}?${params}`)
  },

  async getAllApplicants(perPage = 10, page = 1, statusId = null, nodeId = null) {
    const params = new URLSearchParams({
      per_page: perPage.toString(),
      page: page.toString()
    })

    if (statusId) {
      params.append('status_id', statusId.toString())
    }

    if (nodeId) {
      params.append('node_id', nodeId.toString())
    }

    return apiClient.get(`/applicants?${params}`)
  },

  async confirmApplicant(contractId) {
    const formData = new FormData()
    formData.append('contract_id', contractId)

    return apiClient.post('/node/applicant/confirm', formData)
  },

  async fireWorker(contractId, immediately = false, comment = "") {
    const formData = new FormData()
    formData.append('contract_id', contractId)
    formData.append('immediately', immediately)
    formData.append('comment', comment)

    return apiClient.post('/fired', formData)
  }
}

// Tasks API
export const tasksAPI = {
  async getNodeTasks(nodeId, perPage = 10, page = 1) {
    const params = new URLSearchParams({
      perPage: perPage.toString(),
      page: page.toString()
    })
    return apiClient.get(`/node/${nodeId}/tasks?${params}`)
  },

  async createTask(taskData) {
    return apiClient.post('/tasks', taskData)
  }
}

// Notifications API
export const notificationsAPI = {
  async getNotifications(page = 1, perPage = 20) {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString()
    })
    return apiClient.get(`/notifications?${params}`)
  },

  async markAsRead(notificationId) {
    return apiClient.patch(`/notifications/${notificationId}/read`, {})
  },

  async markMultipleAsRead(notificationIds) {
    return apiClient.post('/notifications/read', {
      notification_ids: notificationIds
    })
  },

  async markAllAsRead() {
    return apiClient.patch('/notifications/read-all', {})
  },

  async deleteNotification(notificationId) {
    return apiClient.delete(`/notifications/${notificationId}`)
  }
}

// Export the main API object
export const api = {
  auth: authAPI,
  network: networkAPI,
  tasks: tasksAPI,
  notifications: notificationsAPI,
  client: apiClient
}

export default api
