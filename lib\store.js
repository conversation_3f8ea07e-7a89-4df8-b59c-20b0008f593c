import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from './api'

const useStore = create(
  persist(
    (set, get) => ({
      // Theme state
      theme: 'light',
      setTheme: (theme) => set({ theme }),

      // Animation settings
      edgeAnimationsEnabled: true,
      setEdgeAnimationsEnabled: (enabled) => set({ edgeAnimationsEnabled: enabled }),

      // Diagram state
      nodes: [],
      edges: [],
      nodeIdCounter: 1,
      

      
      // Add new node
      addNode: (parentId, label) => {
        const { nodes, edges, nodeIdCounter } = get()
        const newNodeId = `node-${nodeIdCounter}`

        // Find parent node
        const parentNode = nodes.find(n => n.id === parentId)
        if (!parentNode) return

        // Calculate position for new node
        const childrenCount = parentNode.data.children.length
        const spacing = 250 // Horizontal spacing between siblings
        const verticalOffset = 150 // Vertical distance from parent

        // Center children around parent
        const startX = parentNode.position.x - (childrenCount * spacing) / 2
        const newPosition = {
          x: startX + childrenCount * spacing,
          y: parentNode.position.y + verticalOffset
        }

        // Create new node
        const newNode = {
          id: newNodeId,
          type: 'custom',
          position: newPosition,
          zIndex: 100, // Standard z-index for regular nodes
          data: {
            label,
            isEditing: false,
            children: []
          }
        }

        // Create new edge with consistent ID pattern
        const newEdge = {
          id: `edge-${parentId}-${newNodeId}`,
          source: parentId,
          target: newNodeId,
          type: 'smoothstep',
          style: {
            stroke: '#10b981', // Green color
            strokeWidth: 2,
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
          }
        }

        // Update parent's children array
        const updatedNodes = nodes.map(node =>
          node.id === parentId
            ? { ...node, data: { ...node.data, children: [...node.data.children, newNodeId] }}
            : node
        )

        set({
          nodes: [...updatedNodes, newNode],
          edges: [...edges, newEdge],
          nodeIdCounter: nodeIdCounter + 1
        })
      },
      
      // Update node label
      updateNodeLabel: (nodeId, label) => {
        const { nodes } = get()
        const updatedNodes = nodes.map(node =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, label } }
            : node
        )
        set({ nodes: updatedNodes })
      },
      
      // Toggle node editing
      toggleNodeEditing: (nodeId) => {
        const { nodes } = get()
        const updatedNodes = nodes.map(node =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, isEditing: !node.data.isEditing } }
            : node
        )
        set({ nodes: updatedNodes })
      },

      // Update node positions
      updateNodePositions: (positionUpdates) => {
        const { nodes } = get()
        const updatedNodes = nodes.map(node =>
          positionUpdates[node.id]
            ? { ...node, position: positionUpdates[node.id] }
            : node
        )
        set({ nodes: updatedNodes })
      },
      
      // Clear diagram
      clearDiagram: () => {
        set({ nodes: [], edges: [], nodeIdCounter: 1 })
      },
      
      // Load user diagram from backend
      loadUserDiagram: async (userId) => {
        try {
          const networkData = await api.network.getNetwork()
          // Convert network data to store format if needed
          // For now, just clear the diagram if no data
          set({ nodes: [], edges: [], nodeIdCounter: 1 })
        } catch (error) {
          console.error('Failed to load diagram from backend:', error)
          // Clear diagram on error
          set({ nodes: [], edges: [], nodeIdCounter: 1 })
        }
      },

      // Save user diagram to backend and localStorage
      saveUserDiagram: async (userId) => {
        const { nodes, edges, nodeIdCounter } = get()

        // Save to localStorage as backup
        localStorage.setItem(`flowmind-diagram-${userId}`, JSON.stringify({
          nodes,
          edges,
          nodeIdCounter
        }))

        // TODO: Implement backend save when API endpoints are available
        try {
          // await api.network.saveDiagram({ nodes, edges, nodeIdCounter })
        } catch (error) {
          console.error('Failed to save diagram to backend:', error)
        }
      },

      // Add node to backend
      addNodeToBackend: async (parentNodeId, position, reward) => {
        try {
          const response = await api.network.addNode({
            position: position,
            reward: reward,
            node_id: parentNodeId
          })

          if (response.success) {
            // Trigger a reload of network data
            get().reloadNetworkData()
            return { success: true, data: response.data }
          } else {
            throw new Error(response.message || 'Failed to add node')
          }
        } catch (error) {
          console.error('Failed to add node to backend:', error)
          throw error
        }
      },

      // Reload network data from backend
      reloadNetworkData: async () => {
        try {
          const networkData = await api.network.getNetwork()
          // Trigger a custom event that the diagram component can listen to
          window.dispatchEvent(new CustomEvent('networkDataUpdated', { detail: networkData }))
          return networkData
        } catch (error) {
          console.error('Failed to reload network data:', error)
          throw error
        }
      },

      // Notifications state
      notifications: [],
      unreadCount: 0,
      notificationsLoading: false,
      notificationsError: null,
      notificationsLoaded: false,

      // Notifications actions
      setNotifications: (notifications) => set({
        notifications,
        unreadCount: notifications.filter(n => !n.read_at).length,
        notificationsLoaded: true
      }),

      addNotification: (notification) => set((state) => {
        const newNotifications = [notification, ...state.notifications]
        return {
          notifications: newNotifications,
          unreadCount: newNotifications.filter(n => !n.read_at).length
        }
      }),

      markNotificationAsRead: (notificationId) => set((state) => {
        const updatedNotifications = state.notifications.map(n =>
          n.id === notificationId ? { ...n, read_at: new Date().toISOString() } : n
        )
        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(n => !n.read_at).length
        }
      }),

      markMultipleNotificationsAsRead: (notificationIds) => set((state) => {
        const updatedNotifications = state.notifications.map(n =>
          notificationIds.includes(n.id) ? { ...n, read_at: new Date().toISOString() } : n
        )
        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(n => !n.read_at).length
        }
      }),

      markAllNotificationsAsRead: () => set((state) => {
        const updatedNotifications = state.notifications.map(n =>
          n.read_at ? n : { ...n, read_at: new Date().toISOString() }
        )
        return {
          notifications: updatedNotifications,
          unreadCount: 0
        }
      }),

      removeNotification: (notificationId) => set((state) => {
        const filteredNotifications = state.notifications.filter(n => n.id !== notificationId)
        return {
          notifications: filteredNotifications,
          unreadCount: filteredNotifications.filter(n => !n.read_at).length
        }
      }),

      loadNotifications: async (page = 1, force = false) => {
        const { notificationsLoaded, notificationsLoading } = get()

        // Skip if already loaded and not forcing reload, or if currently loading
        if ((notificationsLoaded && !force) || notificationsLoading) {
          return
        }

        set({ notificationsLoading: true, notificationsError: null })
        try {
          const response = await api.notifications.getNotifications(page)
          if (response.success) {
            const notifications = response.data?.data || response.data || []
            set({
              notifications,
              unreadCount: notifications.filter(n => !n.read_at).length,
              notificationsLoading: false,
              notificationsLoaded: true
            })
          } else {
            set({
              notificationsError: response.message || 'Failed to load notifications',
              notificationsLoading: false
            })
          }
        } catch (error) {
          console.error('Failed to load notifications:', error)
          set({
            notificationsError: 'Failed to load notifications',
            notificationsLoading: false
          })
        }
      }
    }),
    {
      name: 'flowmind-store',
      partialize: (state) => ({
        theme: state.theme,
        edgeAnimationsEnabled: state.edgeAnimationsEnabled,
        nodes: state.nodes,
        edges: state.edges,
        nodeIdCounter: state.nodeIdCounter
      })
    }
  )
)

export default useStore
